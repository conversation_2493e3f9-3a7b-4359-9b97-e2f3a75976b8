{"name": "acli-rovodev-interactive", "version": "1.0.0", "description": "Node.js interactive wrapper for acli rovodev run command with Chinese UTF-8 support", "main": "acli-rovodev-interactive.js", "scripts": {"start": "node launch-acli.js console", "console": "node launch-acli.js console", "json": "node launch-acli.js json", "stream": "node launch-acli.js stream", "windows": "node windows-acli-fix.js", "diagnose": "node troubleshoot-windows.js diagnose", "fix": "node troubleshoot-windows.js fix", "quick-launch": "node troubleshoot-windows.js launch", "demo": "node complete-usage-example.js", "demo:json": "node complete-usage-example.js json", "demo:stream": "node complete-usage-example.js stream", "demo:hybrid": "node complete-usage-example.js hybrid", "test": "node test-runner.js"}, "keywords": ["acli", "rovodev", "interactive", "cli", "utf8", "chinese"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=14.0.0"}, "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/yourusername/acli-rovodev-interactive.git"}, "config": {"workingDirectory": "D:\\test-list\\rovo-run-list\\test1", "responseTimeout": 3000, "encoding": "utf8"}}