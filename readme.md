# ACLI Rovodev 持续对话工具

一个支持与 `acli rovodev run` 进行无限次持续对话的Node.js工具，专门优化了Windows环境下的中文UTF-8编码支持。

## 🎯 主要特性

- ✅ **持续对话**: 支持无限次连续对话，保持会话状态
- ✅ **多种输出模式**: 控制台、JSON序列化、流式输出
- ✅ **完美中文支持**: UTF-8编码，无乱码问题
- ✅ **Windows优化**: 专门解决Windows编码问题
- ✅ **自动故障排除**: 内置诊断和修复工具
- ✅ **程序化API**: 完整的编程接口

## 🚀 快速开始

### Windows用户（推荐）

如果在Windows环境下遇到编码问题，使用专门的Windows修复版本：

```bash
# 方法1: 快速启动（自动诊断和修复）
npm run quick-launch

# 方法2: 直接使用Windows修复版
npm run windows

# 方法3: 先运行诊断，再启动
npm run diagnose
npm run windows
```

### 通用启动方式

```bash
# 控制台交互模式
npm run console

# JSON序列化模式
npm run json

# 流式JSON模式
npm run stream
```

### 指定工作目录

```bash
node windows-acli-fix.js "D:\your\project\path"
node launch-acli.js console "D:\your\project\path"
```

## 🔧 Windows编码问题解决

如果遇到类似这样的错误：
```
UnicodeEncodeError: 'gbk' codec can't encode character '\u2022'
```

### 自动修复
```bash
# 运行诊断并自动修复
npm run diagnose

# 直接应用修复
npm run fix

# 快速启动（包含修复）
npm run quick-launch
```

### 手动修复
1. 以管理员身份运行命令提示符
2. 执行：`chcp 65001`
3. 设置环境变量：
   ```cmd
   set PYTHONIOENCODING=utf-8
   set PYTHONLEGACYWINDOWSFSENCODING=0
   set PYTHONLEGACYWINDOWSSTDIO=0
   ```
4. 使用Windows修复版启动：`npm run windows`

## 📊 使用模式

### 1. 控制台模式 - 交互式对话
```bash
npm run console
```
- 支持无限次持续对话
- 直观的命令行界面
- 实时显示响应

### 2. JSON模式 - 结构化数据
```bash
npm run json
```
- 返回结构化的JSON响应
- 便于程序处理和集成
- 包含元数据和统计信息

### 3. 流式模式 - 实时数据流
```bash
npm run stream
```
- 实时接收数据块
- 适合处理大量输出
- 支持流式处理

## 💻 编程方式使用

```javascript
const { AcliJsonAPI } = require('./json-api-wrapper');

// 创建API实例
const api = new AcliJsonAPI({
    workingDirectory: 'D:\\your\\project\\path'
});

await api.start();

// 发送单个命令
const result = await api.sendCommand('创建一个Python项目');
console.log(result.response.content);

// 批量发送命令
const commands = [
    '创建React应用',
    '添加路由功能',
    '编写测试用例'
];
const results = await api.sendCommands(commands);

// 获取统计信息
const stats = api.getStats();
console.log('成功率:', stats.completed / stats.total);
```

## 🔍 故障排除

### 常见问题

**1. 编码错误**
```
UnicodeEncodeError: 'gbk' codec can't encode character
```
**解决方案**: 使用 `npm run windows` 或 `npm run quick-launch`

**2. 进程启动失败**
```
Error: spawn ENOENT
```
**解决方案**:
- 检查acli是否已安装: `acli --version`
- 运行诊断: `npm run diagnose`

**3. 工作目录不存在**
**解决方案**:
- 创建目录或使用 `npm run fix` 自动创建
- 指定正确的路径

### 诊断工具

```bash
# 完整环境诊断
npm run diagnose

# 查看所有可能的问题和解决方案
node troubleshoot-windows.js diagnose "你的工作目录"
```

## 📈 高级功能

### 事件监听
```javascript
api.on('conversationComplete', (conversation) => {
    console.log('对话完成:', conversation.id);
    // 自动保存、推送通知等
});

api.on('commandSent', (data) => {
    console.log('命令发送:', data.command);
});
```

### 批量处理
```javascript
// 顺序执行
const results = await api.sendCommands(commands);

// 并发执行
const results = await api.sendCommands(commands, { concurrent: true });
```

### 流式处理
```javascript
streamApi.on('streamChunk', (chunk) => {
    // 实时处理数据块
    processRealTimeData(chunk);
});
```

## 🛠️ 开发和调试

### 开发模式
```bash
# 运行演示
npm run demo

# JSON模式演示
npm run demo:json

# 流式模式演示
npm run demo:stream

# 混合模式演示
npm run demo:hybrid
```

### 日志和监控
所有版本都包含详细的日志记录：
- 会话统计
- 响应时间监控
- 错误追踪
- 重试机制

## 📝 配置

### 环境变量
```bash
# 工作目录
ACLI_WORKING_DIR=D:\your\path

# 响应超时（毫秒）
ACLI_TIMEOUT=10000
```

### 程序配置
```javascript
const options = {
    workingDirectory: 'D:\\your\\path',
    responseTimeout: 10000,
    outputMode: 'console', // 'console', 'json', 'stream'
    maxRetries: 3
};
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License

---

## 🆘 需要帮助？

如果遇到问题：

1. **首先运行**: `npm run diagnose`
2. **查看诊断报告**和建议的解决方案
3. **使用Windows修复版**: `npm run windows`
4. **提交Issue**并附上诊断报告

**Windows用户特别提示**: 这个工具专门优化了Windows环境，如果标准版本不工作，请直接使用 `npm run windows` 或 `npm run quick-launch`。