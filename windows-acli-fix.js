// windows-acli-fix.js - Windows编码问题修复版本

const { spawn, exec } = require('child_process');
const readline = require('readline');
const EventEmitter = require('events');
const util = require('util');
const execAsync = util.promisify(exec);

class WindowsAcliInteractive extends EventEmitter {
    constructor(options = {}) {
        super();

        this.workingDirectory = options.workingDirectory || 'D:\\test-list\\rovo-run-list\\test1';
        this.process = null;
        this.isProcessing = false;
        this.responseBuffer = '';
        this.responseTimeout = null;
        this.responseTimeoutMs = options.responseTimeout || 10000;
        this.outputMode = options.outputMode || 'console';
        this.sessionId = Date.now().toString();
        this.conversationHistory = [];
        this.currentConversation = null;
        this.retryCount = 0;
        this.maxRetries = 3;

        // Windows编码修复标志
        this.windowsEncodingFixed = false;

        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    // Windows编码环境修复
    async setupWindowsEncoding() {
        try {
            console.log('🔧 正在修复Windows编码环境...');

            // 设置控制台代码页为UTF-8
            await execAsync('chcp 65001', {
                cwd: this.workingDirectory,
                encoding: 'utf8',
                windowsHide: true
            });

            console.log('✅ Windows控制台编码已设置为UTF-8');

            // 设置Python环境变量
            process.env.PYTHONIOENCODING = 'utf-8';
            process.env.PYTHONLEGACYWINDOWSFSENCODING = '0';
            process.env.PYTHONLEGACYWINDOWSSTDIO = '0';

            // 禁用某些可能导致编码问题的功能
            process.env.FORCE_COLOR = '0';  // 禁用彩色输出
            process.env.NO_COLOR = '1';     // 明确禁用颜色

            this.windowsEncodingFixed = true;
            return true;

        } catch (error) {
            console.warn('⚠️ Windows编码修复失败，尝试其他方法:', error.message);
            return false;
        }
    }

    // 启动进程的Windows优化版本
    async start() {
        this.log('info', '正在启动 Windows 优化版 acli rovodev run...');
        this.log('info', `工作目录: ${this.workingDirectory}`);

        // 尝试修复Windows编码环境
        await this.setupWindowsEncoding();

        try {
            await this.startProcess();
        } catch (error) {
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                this.log('warn', `启动失败，第 ${this.retryCount} 次重试...`);
                await this.sleep(2000);
                return this.start();
            } else {
                throw new Error(`启动失败，已重试 ${this.maxRetries} 次: ${error.message}`);
            }
        }
    }

    async startProcess() {
        // Windows特定的启动参数
        const spawnOptions = {
            cwd: this.workingDirectory,
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                // Python编码设置
                PYTHONIOENCODING: 'utf-8',
                PYTHONLEGACYWINDOWSFSENCODING: '0',
                PYTHONLEGACYWINDOWSSTDIO: '0',
                // 系统编码设置
                LANG: 'en_US.UTF-8',
                LC_ALL: 'en_US.UTF-8',
                LC_CTYPE: 'en_US.UTF-8',
                // 控制台设置
                TERM: 'dumb',           // 使用简单终端模式
                FORCE_COLOR: '0',       // 禁用颜色输出
                NO_COLOR: '1',          // 明确禁用颜色
                COLORTERM: '',          // 清除颜色终端设置
                // Windows特定
                PYTHONUTF8: '1',        // 强制Python使用UTF-8
                PYTHONUNBUFFERED: '1'   // 禁用缓冲
            },
            // Windows选项
            windowsHide: true,
            shell: false  // 不使用shell，直接执行
        };

        this.log('info', '正在启动acli进程...');

        this.process = spawn('acli', ['rovodev', 'run'], spawnOptions);

        // 设置输出流编码
        this.process.stdout.setEncoding('utf8');
        this.process.stderr.setEncoding('utf8');

        // 监听输出
        this.process.stdout.on('data', (data) => {
            this.handleOutput(data);
        });

        this.process.stderr.on('data', (data) => {
            this.handleError(data);
        });

        this.process.on('close', (code) => {
            this.handleProcessClose(code);
        });

        this.process.on('error', (error) => {
            this.handleProcessError(error);
        });

        this.log('success', 'acli 进程已启动，等待初始化...');
        this.emit('processStarted');

        // 等待初始化
        await this.sleep(5000);  // Windows需要更长的初始化时间

        // 检查进程是否还在运行
        if (!this.process || this.process.killed) {
            throw new Error('进程启动后异常退出');
        }

        this.startPersistentInteraction();
    }

    // 处理错误输出 - Windows版本
    handleError(data) {
        const errorText = data.toString('utf8');

        // 检查是否是编码相关错误
        if (errorText.includes('UnicodeEncodeError') ||
            errorText.includes('gbk') ||
            errorText.includes('illegal multibyte sequence')) {

            this.log('error', 'Windows编码错误检测到', { error: errorText });

            // 尝试编码修复
            this.attemptEncodingFix();
        } else {
            this.log('error', '其他错误', { error: errorText });
        }
    }

    // 处理进程关闭
    handleProcessClose(code) {
        this.log('warn', `进程退出，退出码: ${code}`);

        if (code === 1 && this.retryCount < this.maxRetries) {
            this.retryCount++;
            this.log('info', `检测到编码错误退出，第 ${this.retryCount} 次重试...`);

            setTimeout(async () => {
                try {
                    await this.startProcess();
                } catch (error) {
                    this.log('error', '重试启动失败', { error: error.message });
                }
            }, 3000);
        } else if (code !== 0) {
            this.log('error', '进程异常退出，停止重试');
            this.cleanup();
        }
    }

    // 处理进程错误
    handleProcessError(error) {
        this.log('error', '进程启动失败', { error: error.message });

        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            setTimeout(() => this.start(), 5000);
        } else {
            this.cleanup();
        }
    }

    // 尝试编码修复
    async attemptEncodingFix() {
        this.log('info', '尝试应用编码修复...');

        try {
            // 方法1: 重新设置环境变量
            process.env.PYTHONIOENCODING = 'utf-8:replace';  // 使用replace错误处理
            process.env.TERM = 'dumb';

            // 方法2: 尝试设置区域设置
            await execAsync('powershell -Command "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8"');

            this.log('success', '编码修复应用完成');

        } catch (error) {
            this.log('warn', '编码修复失败', { error: error.message });
        }
    }

    // 处理输出 - Windows优化版
    handleOutput(data) {
        let output = data.toString('utf8');

        // Windows特殊字符处理
        output = this.sanitizeWindowsOutput(output);

        this.responseBuffer += output;

        // 根据输出模式处理
        switch (this.outputMode) {
            case 'console':
                process.stdout.write(output);
                break;
            case 'json':
                this.handleJsonOutput(output);
                break;
            case 'stream':
                this.handleStreamOutput(output);
                break;
        }

        this.detectResponseCompletion();
        this.resetResponseTimeout();
    }

    // Windows输出净化
    sanitizeWindowsOutput(output) {
        // 替换可能导致编码问题的字符
        const replacements = {
            '•': '*',           // 项目符号
            '█': '#',           // 方块字符
            '▮': '=',           // 进度条字符
            '╭': '+',           // 边框字符
            '╮': '+',
            '╰': '+',
            '╯': '+',
            '─': '-',
            '│': '|'
        };

        let sanitized = output;
        for (const [unicode, replacement] of Object.entries(replacements)) {
            sanitized = sanitized.replace(new RegExp(unicode, 'g'), replacement);
        }

        return sanitized;
    }

    // 改进的响应完成检测
    detectResponseCompletion() {
        const buffer = this.responseBuffer;

        // Windows版本的完成检测模式
        const completionPatterns = [
            />\s*$/,                                    // 提示符
            /Session context:\s*[=*]+\s*[\d.]+K?\//,   // 会话信息（净化后）
            /Daily total:\s*[=*]+\s*[\d.]+[KM]?\//,    // 每日统计（净化后）
            /Type "\/" for available commands\./,       // 帮助提示
            /Working in [^\r\n]+/                       // 工作目录信息
        ];

        const isComplete = completionPatterns.some(pattern => pattern.test(buffer));

        if (isComplete && this.isProcessing) {
            this.onResponseComplete();
        }
    }

    // 其他方法保持不变，但添加Windows特定的日志
    log(level, message, data = {}) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            level,
            message,
            sessionId: this.sessionId,
            platform: 'Windows',
            retryCount: this.retryCount,
            ...data
        };

        const prefix = {
            info: '📘',
            success: '✅',
            warn: '⚠️',
            error: '❌'
        }[level] || '📝';

        console.log(`${prefix} [Windows] ${message}`, Object.keys(data).length > 0 ? data : '');
    }

    // 发送命令 - Windows版本
    sendPersistentCommand(command) {
        if (!this.process || this.process.killed) {
            this.log('error', '进程未运行，尝试重启...');
            this.start().then(() => {
                setTimeout(() => this.sendPersistentCommand(command), 3000);
            });
            return;
        }

        this.currentConversation = {
            id: `conv_${Date.now()}`,
            command: command,
            startTime: new Date().toISOString(),
            sessionId: this.sessionId
        };

        this.log('info', `📤 发送命令`, { command, conversationId: this.currentConversation.id });

        this.isProcessing = true;

        try {
            // Windows下使用更安全的写入方式
            const commandBuffer = Buffer.from(command + '\r\n', 'utf8');  // Windows使用CRLF
            this.process.stdin.write(commandBuffer);

            this.emit('commandSent', {
                command,
                conversationId: this.currentConversation.id,
                sessionId: this.sessionId
            });

            this.resetResponseTimeout();

        } catch (error) {
            this.log('error', '发送命令失败', { error: error.message });
            this.isProcessing = false;
            this.waitForNextInput();
        }
    }

    // 从原版本复制其他必要方法
    onResponseComplete() {
        this.isProcessing = false;

        if (this.currentConversation) {
            this.currentConversation.response = this.responseBuffer;
            this.currentConversation.endTime = new Date().toISOString();
            this.currentConversation.duration = Date.now() - new Date(this.currentConversation.startTime).getTime();
            this.conversationHistory.push(this.currentConversation);
        }

        this.emit('responseComplete', {
            conversationId: this.currentConversation?.id,
            response: this.responseBuffer,
            sessionId: this.sessionId
        });

        this.log('success', '响应完成', {
            conversationCount: this.conversationHistory.length,
            responseLength: this.responseBuffer.length
        });

        this.responseBuffer = '';
        this.currentConversation = null;
        this.waitForNextInput();
    }

    startPersistentInteraction() {
        this.log('success', '🚀 Windows持续对话模式已启动！');
        this.log('info', '💡 提示: 输入 "exit" 退出，"stats" 查看统计');
        this.waitForNextInput();
    }

    waitForNextInput() {
        const prompt = this.conversationHistory.length === 0 ?
            '🎯 请输入你的第一个问题: ' :
            `📝 对话 #${this.conversationHistory.length + 1}: `;

        this.rl.question(prompt, (input) => {
            this.handleUserInput(input.trim());
        });
    }

    handleUserInput(input) {
        switch (input.toLowerCase()) {
            case 'exit':
                this.log('info', '正在退出...');
                this.cleanup();
                return;
            case 'stats':
                this.showStats();
                this.waitForNextInput();
                return;
            case 'history':
                this.showHistory();
                this.waitForNextInput();
                return;
            case 'retry':
                this.log('info', '手动重试启动...');
                this.start();
                return;
        }

        if (input === '') {
            this.log('warn', '请输入有效命令');
            this.waitForNextInput();
            return;
        }

        if (this.isProcessing) {
            this.log('warn', '⏳ 正在处理上一个请求，请稍等...');
            this.waitForNextInput();
            return;
        }

        this.sendPersistentCommand(input);
    }

    showStats() {
        const stats = {
            sessionId: this.sessionId,
            totalConversations: this.conversationHistory.length,
            processStatus: this.process && !this.process.killed ? '运行中' : '已停止',
            workingDirectory: this.workingDirectory,
            outputMode: this.outputMode,
            platform: 'Windows',
            retryCount: this.retryCount,
            windowsEncodingFixed: this.windowsEncodingFixed
        };

        console.log('\n📊 Windows会话统计:');
        console.log(JSON.stringify(stats, null, 2));
    }

    showHistory() {
        console.log('\n📚 对话历史:');
        this.conversationHistory.slice(-5).forEach((conv, index) => {
            console.log(`\n对话 #${this.conversationHistory.length - 4 + index}:`);
            console.log(`时间: ${conv.startTime}`);
            console.log(`命令: ${conv.command}`);
            console.log(`耗时: ${conv.duration}ms`);
        });
    }

    resetResponseTimeout() {
        if (this.responseTimeout) {
            clearTimeout(this.responseTimeout);
        }

        this.responseTimeout = setTimeout(() => {
            if (this.isProcessing) {
                this.log('warn', '响应超时，尝试继续...');
                this.onResponseComplete();
            }
        }, this.responseTimeoutMs);
    }

    cleanup() {
        if (this.responseTimeout) {
            clearTimeout(this.responseTimeout);
        }

        if (this.process && !this.process.killed) {
            this.process.kill();
        }

        this.rl.close();

        this.log('info', 'Windows会话结束', {
            totalConversations: this.conversationHistory.length
        });

        this.emit('sessionEnded');
        process.exit(0);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Windows启动脚本
async function windowsMain() {
    console.log('🖥️ Windows ACLI Rovodev 修复版启动器');
    console.log('=' * 50);

    const workingDir = process.argv[2] || 'D:\\test-list\\rovo-run-list\\test1';

    const interactive = new WindowsAcliInteractive({
        workingDirectory: workingDir,
        outputMode: 'console',
        responseTimeout: 10000  // Windows需要更长的超时时间
    });

    // 处理程序退出
    process.on('SIGINT', async () => {
        console.log('\n👋 接收到退出信号，正在清理...');
        await interactive.cleanup();
    });

    try {
        await interactive.start();
    } catch (error) {
        console.error('❌ Windows版本启动失败:', error.message);
        console.log('\n🔧 可能的解决方案:');
        console.log('1. 以管理员身份运行命令提示符');
        console.log('2. 检查 acli 是否正确安装');
        console.log('3. 尝试在PowerShell中运行');
        console.log('4. 检查工作目录是否存在');
        process.exit(1);
    }
}

if (require.main === module) {
    windowsMain();
}

module.exports = { WindowsAcliInteractive };