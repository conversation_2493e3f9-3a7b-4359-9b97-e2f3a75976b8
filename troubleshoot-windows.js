// troubleshoot-windows.js - Windows故障排除和诊断脚本

const { exec, spawn } = require('child_process');
const util = require('util');
const fs = require('fs').promises;
const path = require('path');

const execAsync = util.promisify(exec);

class WindowsTroubleshooter {
    constructor(workingDir = 'D:\\test-list\\rovo-run-list\\test1') {
        this.workingDir = workingDir;
        this.diagnostics = [];
    }

    async runDiagnostics() {
        console.log('🔍 开始Windows环境诊断...\n');

        await this.checkBasicEnvironment();
        await this.checkAcliInstallation();
        await this.checkWorkingDirectory();
        await this.checkPythonEnvironment();
        await this.checkWindowsEncoding();
        await this.testAcliBasic();

        this.showDiagnosticReport();
        return this.diagnostics;
    }

    async checkBasicEnvironment() {
        console.log('📋 检查基本环境...');

        try {
            // 检查Node.js版本
            const nodeVersion = process.version;
            this.addDiagnostic('Node.js版本', nodeVersion, 'success', '✅');

            // 检查操作系统
            const platform = process.platform;
            const arch = process.arch;
            this.addDiagnostic('操作系统', `${platform} ${arch}`, 'info', '📋');

            // 检查当前代码页
            const { stdout: chcpOutput } = await execAsync('chcp');
            const codePage = chcpOutput.trim();
            const isUtf8 = codePage.includes('65001');
            this.addDiagnostic('控制台代码页', codePage, isUtf8 ? 'success' : 'warn', isUtf8 ? '✅' : '⚠️');

            if (!isUtf8) {
                console.log('🔧 尝试设置UTF-8代码页...');
                try {
                    await execAsync('chcp 65001');
                    this.addDiagnostic('UTF-8设置', '已设置为65001', 'success', '✅');
                } catch (error) {
                    this.addDiagnostic('UTF-8设置', '设置失败: ' + error.message, 'error', '❌');
                }
            }

        } catch (error) {
            this.addDiagnostic('基本环境检查', '检查失败: ' + error.message, 'error', '❌');
        }
    }

    async checkAcliInstallation() {
        console.log('🔧 检查ACLI安装...');

        try {
            // 检查acli命令是否可用
            const { stdout, stderr } = await execAsync('acli --version');
            this.addDiagnostic('ACLI版本', stdout.trim(), 'success', '✅');

            // 检查acli路径
            const { stdout: wherePath } = await execAsync('where acli');
            this.addDiagnostic('ACLI路径', wherePath.trim(), 'info', '📁');

        } catch (error) {
            this.addDiagnostic('ACLI安装', '未找到acli命令: ' + error.message, 'error', '❌');
            console.log('💡 解决建议: 请确保ACLI已正确安装并添加到PATH环境变量中');
        }
    }

    async checkWorkingDirectory() {
        console.log('📁 检查工作目录...');

        try {
            const exists = await fs.access(this.workingDir).then(() => true).catch(() => false);

            if (exists) {
                this.addDiagnostic('工作目录', `存在: ${this.workingDir}`, 'success', '✅');

                // 检查目录内容
                const files = await fs.readdir(this.workingDir);
                this.addDiagnostic('目录文件数', files.length.toString(), 'info', '📄');

                // 检查是否有项目文件
                const hasProjectFiles = files.some(f =>
                    f.endsWith('.json') || f.endsWith('.py') || f.endsWith('.js')
                );
                this.addDiagnostic('项目文件', hasProjectFiles ? '发现项目文件' : '空目录', 'info', '📦');

            } else {
                this.addDiagnostic('工作目录', `不存在: ${this.workingDir}`, 'error', '❌');

                // 尝试创建目录
                try {
                    await fs.mkdir(this.workingDir, { recursive: true });
                    this.addDiagnostic('目录创建', '成功创建工作目录', 'success', '✅');
                } catch (createError) {
                    this.addDiagnostic('目录创建', '创建失败: ' + createError.message, 'error', '❌');
                }
            }

        } catch (error) {
            this.addDiagnostic('工作目录检查', '检查失败: ' + error.message, 'error', '❌');
        }
    }

    async checkPythonEnvironment() {
        console.log('🐍 检查Python环境...');

        try {
            // 检查Python版本
            const { stdout: pythonVersion } = await execAsync('python --version');
            this.addDiagnostic('Python版本', pythonVersion.trim(), 'success', '✅');

            // 检查Python编码设置
            const encodingCheck = `
import sys
import locale
print(f"默认编码: {sys.getdefaultencoding()}")
print(f"文件系统编码: {sys.getfilesystemencoding()}")
print(f"标准输出编码: {sys.stdout.encoding}")
print(f"系统区域设置: {locale.getpreferredencoding()}")
            `;

            const { stdout: encodingInfo } = await execAsync(`python -c "${encodingCheck}"`);
            this.addDiagnostic('Python编码信息', encodingInfo.trim(), 'info', '🔤');

        } catch (error) {
            this.addDiagnostic('Python环境', '检查失败: ' + error.message, 'error', '❌');
        }
    }

    async checkWindowsEncoding() {
        console.log('🖥️ 检查Windows编码设置...');

        try {
            // 检查环境变量
            const envVars = [
                'PYTHONIOENCODING',
                'PYTHONLEGACYWINDOWSFSENCODING',
                'PYTHONLEGACYWINDOWSSTDIO',
                'LANG',
                'LC_ALL'
            ];

            envVars.forEach(varName => {
                const value = process.env[varName] || '未设置';
                this.addDiagnostic(`环境变量 ${varName}`, value, 'info', '🔧');
            });

            // 检查PowerShell编码
            const psEncodingCheck = `
[Console]::OutputEncoding.EncodingName
[Console]::InputEncoding.EncodingName
$OutputEncoding.EncodingName
            `;

            try {
                const { stdout: psEncoding } = await execAsync(`powershell -Command "${psEncodingCheck}"`);
                this.addDiagnostic('PowerShell编码', psEncoding.trim(), 'info', '💻');
            } catch (psError) {
                this.addDiagnostic('PowerShell编码', '检查失败: ' + psError.message, 'warn', '⚠️');
            }

        } catch (error) {
            this.addDiagnostic('Windows编码检查', '检查失败: ' + error.message, 'error', '❌');
        }
    }

    async testAcliBasic() {
        console.log('🧪 测试ACLI基本功能...');

        try {
            // 测试acli help命令
            const { stdout: helpOutput } = await execAsync('acli --help', {
                cwd: this.workingDir,
                timeout: 10000
            });

            if (helpOutput.includes('rovodev')) {
                this.addDiagnostic('ACLI Help', 'rovodev命令可用', 'success', '✅');
            } else {
                this.addDiagnostic('ACLI Help', 'rovodev命令不可用', 'warn', '⚠️');
            }

            // 测试在工作目录中运行acli
            console.log('   测试在工作目录中运行acli...');
            const testProcess = spawn('acli', ['rovodev', '--help'], {
                cwd: this.workingDir,
                stdio: ['pipe', 'pipe', 'pipe'],
                env: {
                    ...process.env,
                    PYTHONIOENCODING: 'utf-8:replace',
                    FORCE_COLOR: '0',
                    NO_COLOR: '1'
                },
                timeout: 5000
            });

            let testOutput = '';
            let testError = '';

            testProcess.stdout.on('data', (data) => {
                testOutput += data.toString('utf8');
            });

            testProcess.stderr.on('data', (data) => {
                testError += data.toString('utf8');
            });

            const testResult = await new Promise((resolve) => {
                testProcess.on('close', (code) => {
                    resolve({ code, output: testOutput, error: testError });
                });

                testProcess.on('error', (error) => {
                    resolve({ code: -1, output: '', error: error.message });
                });

                // 5秒超时
                setTimeout(() => {
                    testProcess.kill();
                    resolve({ code: -2, output: '', error: 'Timeout' });
                }, 5000);
            });

            if (testResult.code === 0) {
                this.addDiagnostic('ACLI测试运行', '成功', 'success', '✅');
            } else if (testResult.error.includes('UnicodeEncodeError')) {
                this.addDiagnostic('ACLI测试运行', '编码错误: ' + testResult.error, 'error', '❌');
            } else {
                this.addDiagnostic('ACLI测试运行', `失败 (代码${testResult.code}): ${testResult.error}`, 'error', '❌');
            }

        } catch (error) {
            this.addDiagnostic('ACLI测试', '测试失败: ' + error.message, 'error', '❌');
        }
    }

    addDiagnostic(category, result, level, emoji) {
        this.diagnostics.push({
            category,
            result,
            level,
            emoji,
            timestamp: new Date().toISOString()
        });

        console.log(`   ${emoji} ${category}: ${result}`);
    }

    showDiagnosticReport() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 诊断报告总结');
        console.log('='.repeat(60));

        const counts = {
            success: this.diagnostics.filter(d => d.level === 'success').length,
            error: this.diagnostics.filter(d => d.level === 'error').length,
            warn: this.diagnostics.filter(d => d.level === 'warn').length,
            info: this.diagnostics.filter(d => d.level === 'info').length
        };

        console.log(`✅ 成功: ${counts.success} 项`);
        console.log(`❌ 错误: ${counts.error} 项`);
        console.log(`⚠️ 警告: ${counts.warn} 项`);
        console.log(`📋 信息: ${counts.info} 项`);

        if (counts.error > 0) {
            console.log('\n❌ 发现的问题:');
            this.diagnostics
                .filter(d => d.level === 'error')
                .forEach(d => console.log(`   • ${d.category}: ${d.result}`));
        }

        if (counts.warn > 0) {
            console.log('\n⚠️ 需要注意的问题:');
            this.diagnostics
                .filter(d => d.level === 'warn')
                .forEach(d => console.log(`   • ${d.category}: ${d.result}`));
        }

        console.log('\n💡 建议的解决方案:');
        this.showRecommendations();
    }

    showRecommendations() {
        const hasEncodingError = this.diagnostics.some(d =>
            d.result.includes('UnicodeEncodeError') || d.result.includes('gbk')
        );

        const hasAcliError = this.diagnostics.some(d =>
            d.category.includes('ACLI') && d.level === 'error'
        );

        const hasPathError = this.diagnostics.some(d =>
            d.category.includes('工作目录') && d.level === 'error'
        );

        if (hasEncodingError) {
            console.log('🔤 编码问题解决方案:');
            console.log('   1. 使用管理员权限运行: chcp 65001');
            console.log('   2. 设置环境变量: set PYTHONIOENCODING=utf-8');
            console.log('   3. 使用Windows修复版本启动器');
            console.log('   4. 在PowerShell中运行而不是CMD');
        }

        if (hasAcliError) {
            console.log('🔧 ACLI问题解决方案:');
            console.log('   1. 重新安装ACLI: npm install -g @atlassian/acli');
            console.log('   2. 检查网络连接和代理设置');
            console.log('   3. 确认ACLI账户登录状态');
        }

        if (hasPathError) {
            console.log('📁 路径问题解决方案:');
            console.log('   1. 创建目标目录');
            console.log('   2. 检查目录权限');
            console.log('   3. 使用绝对路径');
        }

        console.log('\n🚀 推荐的启动方式:');
        console.log('   node windows-acli-fix.js "你的工作目录"');
    }

    async applyAutoFixes() {
        console.log('\n🔧 尝试应用自动修复...');

        try {
            // 修复1: 设置代码页
            console.log('   设置UTF-8代码页...');
            await execAsync('chcp 65001');

            // 修复2: 设置环境变量
            console.log('   设置Python环境变量...');
            process.env.PYTHONIOENCODING = 'utf-8:replace';
            process.env.PYTHONLEGACYWINDOWSFSENCODING = '0';
            process.env.PYTHONLEGACYWINDOWSSTDIO = '0';

            // 修复3: 创建工作目录
            console.log('   创建工作目录...');
            await fs.mkdir(this.workingDir, { recursive: true });

            console.log('✅ 自动修复完成');
            return true;

        } catch (error) {
            console.log('❌ 自动修复失败:', error.message);
            return false;
        }
    }

    async generateFixScript() {
        const scriptContent = `
@echo off
echo Windows ACLI 修复脚本
echo ==================

echo 设置UTF-8代码页...
chcp 65001

echo 设置Python环境变量...
set PYTHONIOENCODING=utf-8:replace
set PYTHONLEGACYWINDOWSFSENCODING=0
set PYTHONLEGACYWINDOWSSTDIO=0
set FORCE_COLOR=0
set NO_COLOR=1

echo 创建工作目录...
if not exist "${this.workingDir}" mkdir "${this.workingDir}"

echo 修复完成！
echo 现在可以运行: node windows-acli-fix.js
pause
        `.trim();

        const scriptPath = path.join(process.cwd(), 'fix-windows-acli.bat');
        await fs.writeFile(scriptPath, scriptContent, 'utf8');

        console.log(`\n📝 已生成修复脚本: ${scriptPath}`);
        console.log('   以管理员身份运行此脚本来应用修复');
    }
}

// 启动快速修复版本
class QuickFixLauncher {
    constructor(workingDir) {
        this.workingDir = workingDir;
    }

    async launch() {
        console.log('⚡ 快速修复启动器');
        console.log('==================\n');

        // 应用快速修复
        await this.applyQuickFixes();

        // 启动Windows修复版
        const { WindowsAcliInteractive } = require('./windows-acli-fix');

        const interactive = new WindowsAcliInteractive({
            workingDirectory: this.workingDir,
            outputMode: 'console',
            responseTimeout: 15000
        });

        try {
            await interactive.start();
        } catch (error) {
            console.error('❌ 启动失败，运行完整诊断...');

            const troubleshooter = new WindowsTroubleshooter(this.workingDir);
            await troubleshooter.runDiagnostics();
            await troubleshooter.generateFixScript();
        }
    }

    async applyQuickFixes() {
        console.log('🔧 应用快速修复...');

        try {
            // 设置代码页
            await execAsync('chcp 65001').catch(() => {});

            // 设置关键环境变量
            Object.assign(process.env, {
                PYTHONIOENCODING: 'utf-8:replace',
                PYTHONLEGACYWINDOWSFSENCODING: '0',
                PYTHONLEGACYWINDOWSSTDIO: '0',
                FORCE_COLOR: '0',
                NO_COLOR: '1',
                TERM: 'dumb'
            });

            // 创建工作目录
            await fs.mkdir(this.workingDir, { recursive: true }).catch(() => {});

            console.log('✅ 快速修复完成\n');

        } catch (error) {
            console.log('⚠️ 部分修复失败，但将尝试继续...\n');
        }
    }
}

// 主程序
async function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'diagnose';
    const workingDir = args[1] || 'D:\\test-list\\rovo-run-list\\test1';

    console.log('🖥️ Windows ACLI 故障排除工具');
    console.log('============================\n');

    switch (command) {
        case 'diagnose':
            const troubleshooter = new WindowsTroubleshooter(workingDir);
            const diagnostics = await troubleshooter.runDiagnostics();

            // 询问是否应用自动修复
            if (diagnostics.some(d => d.level === 'error')) {
                console.log('\n❓ 是否尝试自动修复? (y/n)');
                process.stdin.setRawMode(true);
                process.stdin.resume();
                process.stdin.on('data', async (key) => {
                    if (key.toString() === 'y' || key.toString() === 'Y') {
                        console.log('\n🔧 应用自动修复...');
                        await troubleshooter.applyAutoFixes();
                        await troubleshooter.generateFixScript();
                    }
                    process.exit(0);
                });
            }
            break;

        case 'fix':
            const troubleshooter2 = new WindowsTroubleshooter(workingDir);
            await troubleshooter2.applyAutoFixes();
            await troubleshooter2.generateFixScript();
            break;

        case 'launch':
            const launcher = new QuickFixLauncher(workingDir);
            await launcher.launch();
            break;

        default:
            console.log('用法:');
            console.log('  node troubleshoot-windows.js diagnose [工作目录] - 运行诊断');
            console.log('  node troubleshoot-windows.js fix [工作目录]      - 应用修复');
            console.log('  node troubleshoot-windows.js launch [工作目录]   - 快速启动');
            break;
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { WindowsTroubleshooter, QuickFixLauncher };